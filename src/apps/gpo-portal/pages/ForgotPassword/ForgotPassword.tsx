import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { ForgotPasswordForm } from '@/libs/auth';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

interface FormValues {
  email: string;
}

export const ForgotPassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleResetPassword, isLoading } = useAsyncRequest({
    apiFunc: async (values?: FormValues) => {
      if (!values) return;

      await post({
        url: '/gpo/password-resets',
        body: values,
      });

      successNotification(t('gpo.forgotPassword.resetMessage'));
      navigate(GPO_ROUTES_PATH.login);
    },
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const handleSubmit = async (values: FormValues) => {
    await handleResetPassword(values);
  };

  return (
    <ForgotPasswordForm
      onSubmit={handleSubmit}
      isLoading={isLoading}
      loginPath={GPO_ROUTES_PATH.login}
      schema={SCHEMA}
      namespace="gpo.forgotPassword"
    />
  );
};
