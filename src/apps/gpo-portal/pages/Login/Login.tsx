import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { LoginForm } from '@/libs/auth';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';

interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const Login = () => {
  const navigate = useNavigate();
  const { setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleLogin, isLoading } = useAsyncRequest({
    apiFunc: async (values?: FormValues) => {
      if (!values) return;

      const { email, password } = values;

      await post<UserType>({
        url: '/sessions',
        body: { email, password },
        withApi: false,
      });

      navigate(GPO_ROUTES_PATH.dashboard);
    },
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const handleSubmit = async (values: FormValues) => {
    await handleLogin(values);
  };

  return (
    <LoginForm
      onSubmit={handleSubmit}
      isLoading={isLoading}
      forgotPasswordPath={GPO_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
      namespace="gpo.login"
    />
  );
};
