import { useFeatureFlags, useFeatureFlag } from '@/utils';
import { FEATURE_FLAGS } from '@/constants';

/**
 * Demo component showing how to use feature flags with URL overrides
 * 
 * Try these URLs:
 * - ?PRODUCT_REVIEWS=true
 * - ?SUBSTITUTES=true&PRODUCT_REVIEWS=false
 * - ?SEARCH_RESULTS_ORDER=true&PURCHASING_PREFERENCES=true
 */
export const FeatureFlagDemo = () => {
  // Get all feature flags with URL overrides
  const flags = useFeatureFlags();
  
  // Get individual feature flags
  const isProductReviewsEnabled = useFeatureFlag('PRODUCT_REVIEWS');
  const isSubstitutesEnabled = useFeatureFlag('SUBSTITUTES');
  const isSearchOrderEnabled = useFeatureFlag('SEARCH_RESULTS_ORDER');

  return (
    <div className="p-6 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Feature Flag Demo</h3>
      
      <div className="mb-6">
        <h4 className="font-medium mb-2">Try these URL parameters:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• <code>?PRODUCT_REVIEWS=true</code></li>
          <li>• <code>?SUBSTITUTES=true&PRODUCT_REVIEWS=false</code></li>
          <li>• <code>?SEARCH_RESULTS_ORDER=true&PURCHASING_PREFERENCES=true</code></li>
        </ul>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Default Values:</h4>
          <div className="space-y-1 text-sm">
            {Object.entries(FEATURE_FLAGS).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span>{key}:</span>
                <span className={value ? 'text-green-600' : 'text-red-600'}>
                  {value.toString()}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Current Values (with URL overrides):</h4>
          <div className="space-y-1 text-sm">
            {Object.entries(flags).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span>{key}:</span>
                <span className={value ? 'text-green-600' : 'text-red-600'}>
                  {value.toString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-white rounded border">
        <h4 className="font-medium mb-2">Feature Examples:</h4>
        
        {isProductReviewsEnabled && (
          <div className="mb-2 p-2 bg-green-100 rounded">
            ✅ Product Reviews feature is enabled!
          </div>
        )}
        
        {isSubstitutesEnabled && (
          <div className="mb-2 p-2 bg-blue-100 rounded">
            ✅ Product Substitutes feature is enabled!
          </div>
        )}
        
        {isSearchOrderEnabled && (
          <div className="mb-2 p-2 bg-purple-100 rounded">
            ✅ Search Results Order feature is enabled!
          </div>
        )}
        
        {!isProductReviewsEnabled && !isSubstitutesEnabled && !isSearchOrderEnabled && (
          <div className="p-2 bg-gray-100 rounded text-gray-600">
            No optional features are currently enabled. Try adding URL parameters!
          </div>
        )}
      </div>
    </div>
  );
};
