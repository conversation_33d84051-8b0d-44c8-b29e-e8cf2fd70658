import { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';

export const useProductSuggestions = (query = '') => {
  const search = query.trim().toLowerCase();
  const [lastQueryWithNoResults, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(search, 300);
  const queryClient = useQueryClient();

  const queryKey = queryKeys.products.suggestions(
    clinic?.id || '',
    debouncedQuery,
  );

  const shouldSkipQuery = () => {
    if (debouncedQuery?.length <= 2 || !clinic?.id) {
      return true;
    }

    const noResultsQuery =
      lastQueryWithNoResults &&
      debouncedQuery.startsWith(lastQueryWithNoResults);

    return noResultsQuery;
  };

  const { data: searchSuggestions = [], isLoading: isSuggestionsLoading } =
    useQuery({
      queryKey,
      queryFn: async () => {
        try {
          const response = await get<{ data: string[] }>({
            url: `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(debouncedQuery)}&clinicId=${clinic?.id}`,
          });

          const results = response?.data || [];

          if (results.length === 0) {
            setLastEmptyQuery(debouncedQuery);
          } else {
            setLastEmptyQuery('');
          }

          return results;
        } catch (error) {
          setLastEmptyQuery(debouncedQuery);
          throw error;
        }
      },
      enabled: !shouldSkipQuery(),
      staleTime: 5 * 60 * 1000,
    });

  useEffect(() => {
    const isBackSpacing =
      lastQueryWithNoResults &&
      debouncedQuery.length < lastQueryWithNoResults.length;

    if (isBackSpacing) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, lastQueryWithNoResults]);

  return {
    searchSuggestions,
    isSuggestionsLoading,
  };
};
