import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Text, Title } from '@mantine/core';
import { LastOrdersPanel } from './components/LastOrdersPanel/LastOrdersPanel';
import { EstimatedRebatesPanel } from './components/EstimatedRebatesPanel/EstimatedRebatesPanel';
import { OverviewPanel } from './components/OverviewPanel/OverviewPanel';
import { PromoMatcher } from './components/PromoMatcher/PromoMatcher';

export const ClinicDashboard = () => {
  const { user } = useAuthStore();

  return (
    <div className="main gap-10">
      <div>
        <Title order={3} size="h4" mb=".25rem">
          Welcome {user?.name}
        </Title>

        <Text size="md">
          View your clinics spend, rebate tracking, and order history below.
        </Text>
      </div>
      <PromoMatcher />
      <OverviewPanel />
      <EstimatedRebatesPanel />
      <LastOrdersPanel />
    </div>
  );
};
