import { Text } from '@mantine/core';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { OrderHistoryItem } from '@/apps/shop/pages/OrderHistory/components/OrderHistoryItem/OrderHistoryItem';
import { useOrderList } from '@/apps/shop/pages/OrderHistory/services/useOrderList';
import styles from './LastOrdersPanel.module.css';
import { PoweredBy } from '@/libs/ui/PoweredBy/PoweredBy';

export const LastOrdersPanel = () => {
  const { orders } = useOrderList({ limit: 4 });

  return (
    <CollapsiblePanel
      header={
        <>
          <h3 className="px-6 py-[1.3rem] text-xl font-medium">
            Promo Matcher
          </h3>
          <PoweredBy />
        </>
      }
      content={
        <div className="flex-col bg-white p-6">
          <h4 className="text-lg font-medium">Handpicked Deals for You!</h4>
          <span className="mb-6 text-sm text-black/80">
            We found the best deals for you - don&apos;t wait!
          </span>

          <div className="bg-[rgba(0,0,0,0.02)] p-4">
            {orders.length ? (
              orders.map((order) => (
                <div
                  key={order.id}
                  className={`mb-4 bg-[rgba(0,0,0,0.02)] ${styles.orderItem}`}
                >
                  <OrderHistoryItem order={order} isActive={false} />
                </div>
              ))
            ) : (
              <Text>No order created yet!</Text>
            )}
          </div>
        </div>
      }
      startOpen
    />
  );
};
