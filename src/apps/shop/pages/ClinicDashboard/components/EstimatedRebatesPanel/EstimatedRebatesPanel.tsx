import { Menu, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Badge } from '@/libs/ui/Badge/Badge';
import { EstimateRebateVendorPanel } from '../EstimateRebateVendorPanel/EstimateRebateVendorPanel';
import { EstimatedRebatesPanelLoader } from './EstimatedRebatesPanelLoader';
import { getPriceString } from '@/utils';
import { RebateType } from '@/types/common';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { useCallback } from 'react';
import { get } from '@/libs/utils/api';
import { useRebatesPeriod } from '../../utils/useRebatesPeriod';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';

export const EstimatedRebatesPanel = () => {
  const {
    options: rebatePeriodsOptions,
    period: selectedPeriod,
    setPeriod,
    endDate,
    startDate,
  } = useRebatesPeriod();

  const loadRebatesFunc = useCallback(async () => {
    const response = await get<RebateType[]>({
      url: '/rebate-estimates',
    });
    return response;
  }, []);

  const { data: rebates, isLoading } = useQuery({
    queryKey: queryKeys.rebates.estimates(),
    queryFn: loadRebatesFunc,
  });

  if (isLoading) {
    return <EstimatedRebatesPanelLoader />;
  }

  if (!rebates || rebates.length === 0) {
    return null;
  }

  const rebatesOnThePeriod = rebates.filter(({ promotion }) => {
    const promoStart = dayjs(promotion.startedAt);
    const promoEnd = dayjs(promotion.endedAt);

    const endsAfterOrOnStart =
      promoEnd.isAfter(startDate) || promoEnd.isSame(startDate, 'day');

    const startsBeforeOrOnEnd =
      promoStart.isBefore(endDate) || promoStart.isSame(endDate, 'day');

    return endsAfterOrOnStart && startsBeforeOrOnEnd;
  });

  const rebateData = rebatesOnThePeriod.reduce<{
    estimatedRebateAmount: number;
    vendorRebates: Record<string, RebateType[]>;
  }>(
    (acc, rebate) => ({
      estimatedRebateAmount:
        +rebate.estimatedRebateAmount + acc.estimatedRebateAmount,
      vendorRebates: {
        ...acc.vendorRebates,
        [rebate.promotion.vendor.id]: [
          ...(acc.vendorRebates[rebate.promotion.vendor.id] ?? []),
          rebate,
        ],
      },
    }),
    {
      estimatedRebateAmount: 0,
      vendorRebates: {},
    },
  );

  return (
    <CollapsiblePanel
      header={
        <Flex
          pl="1.5rem"
          pr="4rem"
          py="1rem"
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <Title order={4} fw="500" mr="md">
              Rebate Programs
            </Title>
            <Badge variant="gradient" background="white" color="#333">
              <Flex align="center" gap="md">
                <Text size="1rem" fw="500" tt="none">
                  Estimated Gross Earnings
                </Text>
                <Text size="1.5rem" fw="500" lh="3.3rem">
                  {getPriceString(rebateData.estimatedRebateAmount)}
                </Text>
              </Flex>
            </Badge>
            <div className="ml-4">
              <HelpTooltip message="Projected rebate earnings on eligible Highfive purchases. For reference only — contact your GPO for final rebate amounts." />
            </div>
          </Flex>
          <Menu>
            <Menu.Target>
              <Flex align="center" miw="12rem">
                <Text miw="4.5rem">Filter by</Text>
                <Button variant="white">
                  <Text tt="capitalize" size="0.875rem" fw="500">
                    {selectedPeriod}
                  </Text>
                </Button>
              </Flex>
            </Menu.Target>
            <Menu.Dropdown>
              {rebatePeriodsOptions.map((option) =>
                option !== selectedPeriod ? (
                  <Menu.Item key={option} onClick={() => setPeriod(option)}>
                    <Text tt="capitalize" size="0.875rem" miw="100px">
                      {option}
                    </Text>
                  </Menu.Item>
                ) : null,
              )}
            </Menu.Dropdown>
          </Menu>
        </Flex>
      }
      content={
        <Flex direction="column" p="1.5rem">
          <Text c="#666" size="0.75rem" mb="0.75rem">
            Results filtered by:{' '}
            <Text c="#333" fw="700" tt="capitalize" span>
              {selectedPeriod} ({startDate.format(DEFAULT_DISPLAY_DATE_FORMAT)}{' '}
              - {endDate.format(DEFAULT_DISPLAY_DATE_FORMAT)})
            </Text>
          </Text>
          <Flex direction="column" gap="md">
            {Object.keys(rebateData.vendorRebates).map((vendorId) => (
              <EstimateRebateVendorPanel
                key={vendorId}
                rebates={rebateData.vendorRebates[vendorId]}
              />
            ))}
          </Flex>
        </Flex>
      }
      startOpen
    />
  );
};
