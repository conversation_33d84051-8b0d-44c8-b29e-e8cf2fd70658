import { Divider } from '@mantine/core';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import styles from './PromoMatcher.module.css';
import { PoweredBy } from '@/libs/ui/PoweredBy/PoweredBy';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import { Button } from '@/libs/ui/Button/Button';

export const PromoMatcher = () => {
  return (
    <CollapsiblePanel
      header={
        <>
          <h3 className="px-6 py-[1.3rem] text-xl font-medium">
            Promo Matcher
          </h3>
          <PoweredBy />
        </>
      }
      content={
        <div className="flex flex-col">
          <div className="flex-col bg-white p-6">
            <h3 className="text-lg font-medium">Handpicked Deals for You!</h3>
            <p className="mb-8 text-sm text-black/80">
              We found the best deals for you - don&apos;t wait!
            </p>

            <div className="relative max-h-48 rounded-sm border-1 border-blue-400 bg-white p-5">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <DiscountIcon className="h-6 w-6" />
                    <span className="text-sm font-medium text-black">
                      Promotion • (promo.type)
                    </span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Buy 10 Pivetal® Suffusion™ products, get 2 free. Kind for
                    kind
                  </h4>
                  <p className="max-w-5/6 pt-2 pb-1 text-xs tracking-wider text-black/80">
                    You purchased Pivetal® Suffusion™ products 12 units in the
                    past 6 months (from September 2024 to February 2025). If you
                    take advantage of this promotion, you could save $45 more
                    compared to your previous purchase periods!
                  </p>
                </div>

                <Button size="sm" className={styles.takeDealBtn}>
                  Take Deal
                </Button>
              </div>
              <Divider my="xs" />

              <div className="align-center flex gap-3">
                <span>
                  <span className="mr-1 text-xs text-black/60">Vendor:</span>
                  <span className="text-xs font-semibold">Pivetal</span>
                </span>
                <Divider orientation="vertical" />
                <span>
                  <span className="mr-1 text-xs text-black/60">Category:</span>
                  <span className="text-xs font-semibold">Canine, Feline</span>
                </span>
                <Divider orientation="vertical" />
                <span>
                  <span className="mr-1 text-xs text-black/60">End Date:</span>
                  <span className="text-xs font-semibold">03/31/2025</span>
                </span>
                <p className="ml-auto">
                  <span className="mr-1 text-xs text-black/60">End Date:</span>
                  <span className="text-xs font-semibold">03/31/2025</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      }
      startOpen
    />
  );
};
