import { Button } from '@/libs/ui/Button/Button';
import { Divider } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';

export const PromoItem = () => {
  return (
    <div className="relative rounded-sm border-1 border-blue-400 bg-white p-5">
      <div className="flex items-center justify-between">
        <div className="flex-7">
          <div className="flex items-center gap-2">
            <DiscountIcon className="h-6 w-6" />
            <span className="text-sm font-medium text-black">
              Promotion • (promo.type)
            </span>
          </div>
          <h4 className="text-lg font-semibold text-gray-900">
            Buy 10 Pivetal® Suffusion™ products, get 2 free. Kind for kind
          </h4>
          <p className="max-h-18 overflow-hidden pt-2 pb-1 text-xs tracking-wider text-black/80 transition-all duration-1000 ease-in-out hover:max-h-72 hover:cursor-pointer hover:overflow-y-auto hover:pr-2">
            You purchased Pivetal® Suffusion™ products 12 units in the past 6
            months (from September 2024 to February 2025). If you take advantage
            of this promotion, you could save $45 more compared to your previous
            purchase periods!
          </p>
        </div>

        <div className="flex flex-3 justify-end">
          <Button size="sm" className={styles.takeDealBtn}>
            Take Deal
          </Button>
        </div>
      </div>
      <Divider my="xs" />

      <div className="align-center flex gap-3">
        <span>
          <span className="mr-1 text-xs text-black/60">Vendor:</span>
          <span className="text-xs font-semibold">Pivetal</span>
        </span>
        <Divider orientation="vertical" />
        <span>
          <span className="mr-1 text-xs text-black/60">Category:</span>
          <span className="text-xs font-semibold">Canine, Feline</span>
        </span>
        <Divider orientation="vertical" />
        <span>
          <span className="mr-1 text-xs text-black/60">End Date:</span>
          <span className="text-xs font-semibold">03/31/2025</span>
        </span>
        <p className="ml-auto">
          <span className="mr-1 text-xs text-black/60">End Date:</span>
          <span className="text-xs font-semibold">03/31/2025</span>
        </p>
      </div>
    </div>
  );
};
