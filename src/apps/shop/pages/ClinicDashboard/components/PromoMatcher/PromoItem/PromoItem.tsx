import { Button } from '@/libs/ui/Button/Button';
import { Divider } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import styles from './PromoItem.module.css';

export interface PromoItemData {
  id: string;
  promoType: string;
  title: string;
  description: string;
  vendor: string;
  category: string;
  endDate: string;
  savings?: string;
}

interface PromoItemProps {
  data: PromoItemData;
}

export const PromoItem = ({ data }: PromoItemProps) => {
  return (
    <div className="relative rounded-sm bg-white p-5 hover:border-blue-400">
      <div className="flex items-center justify-between">
        <div className="flex-7">
          <div className="flex items-center gap-2">
            <DiscountIcon className="h-6 w-6" />
            <span className="text-sm font-medium text-black">
              Promotion • {data.promoType}
            </span>
          </div>
          <h4 className="text-lg font-semibold text-gray-900">{data.title}</h4>
          <p className="max-h-18 overflow-hidden pt-2 pb-1 text-xs tracking-wider text-black/80 transition-all duration-1000 ease-in-out hover:max-h-72 hover:cursor-pointer hover:overflow-y-auto hover:pr-2">
            {data.description}
          </p>
        </div>

        <div className="flex flex-3 justify-end">
          <Button size="sm" className={styles.takeDealBtn}>
            Take Deal
          </Button>
        </div>
      </div>
      <Divider my="xs" />

      <div className="grid grid-cols-4">
        <span>
          <span className="mr-1 text-xs text-black/60">Vendor:</span>
          <span className="text-xs font-semibold">{data.vendor}</span>
        </span>
        <span>
          <span className="mr-1 text-xs text-black/60">Category:</span>
          <span className="text-xs font-semibold">{data.category}</span>
        </span>
        <span>
          <span className="mr-1 text-xs text-black/60">End Date:</span>
          <span className="text-xs font-semibold">{data.endDate}</span>
        </span>
        {data.savings && (
          <p className="ml-auto">
            <span className="mr-1 text-xs text-black/60">Savings:</span>
            <span className="text-xs font-semibold">{data.savings}</span>
          </p>
        )}
      </div>
    </div>
  );
};
