import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { ChangePasswordForm } from '@/libs/auth';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

interface FormValues {
  password: string;
  confirmPassword: string;
}

export const ChangePassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleChangePassword, isLoading } = useAsyncRequest({
    apiFunc: async (values?: FormValues) => {
      if (!values) return;

      await post({
        url: '/password-resets/confirm',
        body: values,
      });

      successNotification(t('changePassword.successMessage'));
      navigate(SHOP_ROUTES_PATH.login);
    },
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const handleSubmit = async (values: FormValues) => {
    await handleChangePassword(values);
  };

  return (
    <ChangePasswordForm
      onSubmit={handleSubmit}
      isLoading={isLoading}
      schema={SCHEMA}
    />
  );
};
