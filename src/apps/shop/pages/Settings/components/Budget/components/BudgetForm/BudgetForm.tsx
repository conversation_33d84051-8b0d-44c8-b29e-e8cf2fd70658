import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { LoadingOverlay, UnstyledButton } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useTranslation } from 'react-i18next';
import { Resolver, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { Button } from '@/libs/ui/Button/Button';
import { useSettingsStore } from '@/apps/shop/stores/useSettingsStore/useSettingsStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';

import { SCHEMA } from './constants';
import { FormValues } from '../../interfaces';
import { transformValueToForm, transformValueToSend } from './utils';
import { BUDGET_TYPE } from '@/libs/cart/constants';
import { Input } from '@/libs/form/Input';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import styles from './BudgetForm.module.css';
import { integerMoneyMask } from '@/libs/form/masks';
import { Checkbox } from '@/libs/form/Checkbox';

interface BudgetFormProps {
  onComplete: VoidFunction;
}
export const BudgetForm = ({ onComplete }: BudgetFormProps) => {
  const { t } = useTranslation();
  const [includeExternalData, setIncludeExternalData] = useState(false);
  const { getBudget, budget, updateBudget } = useSettingsStore();
  const [budgetStrategy, setBudgetStrategy] = useState<
    'STATIC' | 'DYNAMIC' | null
  >(null);
  const {
    reset,
    register,
    handleSubmit,
    clearErrors,
    setError,
    formState: { errors, isValid },
  } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA) as unknown as Resolver<FormValues>,
  });
  const hasStaticType = budgetStrategy === BUDGET_TYPE.STATIC;

  useEffect(() => {
    if (budget) {
      setIncludeExternalData(budget.includeExternalData);
      setBudgetStrategy(budget.type);
    }
  }, [budget, setIncludeExternalData, setBudgetStrategy]);

  const { apiRequest: getBudgetApi, isLoading: isLoadingGet } = useAsyncRequest(
    {
      apiFunc: getBudget,
    },
  );

  useEffect(() => {
    getBudgetApi(reset);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { apiRequest: handleUpdateBudget, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await updateBudget(
        transformValueToSend({
          ...values,
          includeExternalData,
          type: budgetStrategy!,
        }),
      );

      successNotification(
        t('client.settings.updatedSettings', {
          tab: t('client.settings.budget.title'),
        }),
      );
      onComplete();
    }),
    errorFunc: (error) => {
      defaultFormErrorHandler(error, setError);
    },
  });

  useEffect(() => {
    if (!budget) {
      return;
    }

    setBudgetStrategy(budget.type);
    reset(transformValueToForm(budget));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [budget]);

  useEffect(() => {
    clearErrors();
  }, [budgetStrategy, clearErrors]);

  return (
    <div>
      <LoadingOverlay visible={isLoadingGet} />
      <Flex justify="center" mb="md">
        <UnstyledButton
          onClick={() => setBudgetStrategy(BUDGET_TYPE.STATIC)}
          className={clsx(styles.budgetTypeButton, {
            [styles.budgetTypeButtonSelected]: hasStaticType,
          })}
        >
          Static Budget
        </UnstyledButton>
        <UnstyledButton
          onClick={() => setBudgetStrategy(BUDGET_TYPE.DYNAMIC)}
          className={clsx(styles.budgetTypeButton, {
            [styles.budgetTypeButtonSelected]: !hasStaticType,
          })}
        >
          Dynamic Budget
        </UnstyledButton>
      </Flex>

      <form id="form" onSubmit={handleUpdateBudget}>
        {budgetStrategy === 'STATIC' ? (
          <>
            <Flex gap="1rem" mb="24px">
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={
                    <>
                      {t('client.settings.budget.weeklyCOGS')}{' '}
                      <HelpTooltip
                        message={t('client.settings.budget.COGSHelp')}
                      />
                    </>
                  }
                  {...register('weeklyCogs')}
                  error={errors.weeklyCogs?.message}
                />
              </div>
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={
                    <>
                      {t('client.settings.budget.weeklyGA')}{' '}
                      <HelpTooltip
                        message={t('client.settings.budget.GAHelp')}
                      />
                    </>
                  }
                  {...register('weeklyGa')}
                  error={errors.weeklyGa?.message}
                />
              </div>
            </Flex>
            <Flex gap="1rem" mb="24px">
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={t('client.settings.budget.monthlyCOGS')}
                  {...register('monthlyCogs')}
                  error={errors.monthlyCogs?.message}
                />
              </div>
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={t('client.settings.budget.monthlyGA')}
                  {...register('monthlyGa')}
                  error={errors.monthlyGa?.message}
                />
              </div>
            </Flex>
          </>
        ) : null}

        {budgetStrategy === 'DYNAMIC' ? (
          <>
            <Flex gap="1rem" mb="24px">
              <div className="flex-1">
                <Input
                  label={
                    <>
                      {t('client.settings.budget.COGSTarget')}
                      {' % '}
                      <HelpTooltip
                        message={t('client.settings.budget.COGSHelp')}
                      />
                    </>
                  }
                  {...register('targetCogsPercent')}
                  error={errors.targetCogsPercent?.message}
                />
              </div>
              <div className="flex-1">
                <Input
                  label={
                    <>
                      {t('client.settings.budget.GATarget')}
                      {' % '}
                      <HelpTooltip
                        message={t('client.settings.budget.GAHelp')}
                      />
                    </>
                  }
                  {...register('targetGaPercent')}
                  error={errors.targetGaPercent?.message}
                />
              </div>
            </Flex>
            <Flex gap="1rem" mb="24px">
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={t('client.settings.budget.avgTwoWeeksSales')}
                  {...register('avgTwoWeeksSales')}
                  error={errors.avgTwoWeeksSales?.message}
                />
              </div>
              <div className="flex-1">
                <Input
                  mask={integerMoneyMask}
                  label={t('client.settings.budget.monthToDateSales')}
                  {...register('monthToDateSales')}
                  error={errors.monthToDateSales?.message}
                />
              </div>
            </Flex>
          </>
        ) : null}

        {includeExternalData ? (
          <Flex gap="1rem" my="24px">
            <div className="flex-1">
              <Input
                mask={integerMoneyMask}
                label={t('client.settings.budget.externalWeeklyCogs')}
                {...register('externalWeeklyCogs')}
                error={errors.monthToDateSales?.message}
              />
            </div>
            <div className="flex-1">
              <Input
                mask={integerMoneyMask}
                label={t('client.settings.budget.externalMonthlyCogs')}
                {...register('externalMonthlyCogs')}
                error={errors.externalMonthlyCogs?.message}
              />
            </div>
          </Flex>
        ) : null}

        <div className="mb-6">
          <Checkbox
            label={t('client.settings.budget.costsInCOGS')}
            checked={includeExternalData}
            onChange={() => {
              setIncludeExternalData(!includeExternalData);
            }}
          />
        </div>
      </form>

      <Button
        form="form"
        variant="secondary"
        loading={isLoading}
        disabled={!isValid}
      >
        {t('common.save')}
      </Button>
    </div>
  );
};
