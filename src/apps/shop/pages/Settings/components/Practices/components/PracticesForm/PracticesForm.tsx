import { MultiSelect } from '@/libs/form/MultiSelect';
import { Select } from '@/libs/form/Select';
import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { createSchema, practiceTypeOptions, speciesFocusOptions } from './constants';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { post } from '@/libs/utils/api';
import { shoppingPreferencesOptions } from '../../constants';
import { useFeatureFlag } from '@/utils';

interface PracticesFormProps {
  clinicId: string;
  onComplete: VoidFunction;
  defaultValues: {
    examRoomsCount: number;
    fulltimeDvmCount: number;
    speciesFocus: string;
    practiceTypes: string[];
    primaryShoppingPreference?: string;
    secondaryShoppingPreferences?: string[];
  };
}
export const PracticesForm = ({
  clinicId,
  defaultValues,
  onComplete,
}: PracticesFormProps) => {
  const isPurchasingPreferencesEnabled = useFeatureFlag('PURCHASING_PREFERENCES');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
  } = useForm({
    resolver: yupResolver(createSchema(isPurchasingPreferencesEnabled)),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { apiRequest: handleUpdatePractices, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (data) => {
      await post({
        url: `/clinics/${clinicId}`,
        method: 'PATCH',
        body: {
          ...data,
        },
      });

      successNotification('Pratices was updated');
      onComplete();
    }),
    errorFunc: (error) => {
      defaultFormErrorHandler(error, setError);
    },
  });

  return (
    <form
      className="mt-3 rounded border border-black/[0.04] bg-black/[0.02] p-6"
      onSubmit={handleUpdatePractices}
    >
      <Text c="#333" size="14px">
        Attention, changing this preferences will change the way your offers are
        being.
      </Text>
      <div className="mt-3 rounded border border-black/[0.04] bg-white p-6">
        <Text c="#344054" size="16px" fw="500">
          Practice Information
        </Text>
        <Divider my="md" />
        <div className="mb-4">
          <Select
            label="Species Focus"
            tooltip="What types of animals do you treat most often?"
            options={speciesFocusOptions}
            {...register('speciesFocus')}
            error={errors.speciesFocus?.message}
          />
        </div>
        <div className="mb-4">
          <MultiSelect
            label="Type of Practice"
            tooltip="What best describes your clinic's specialty?"
            {...register('practiceTypes')}
            placeholder="Select options"
            defaultValue={defaultValues.practiceTypes}
            options={practiceTypeOptions}
          />
        </div>

        <Flex gap="1rem" mb="24px">
          <div className="flex-1">
            <Input
              label="Fulltime DVM"
              tooltip="How many full-time veterinarians do you have?"
              type="number"
              {...register('fulltimeDvmCount')}
              error={errors.fulltimeDvmCount?.message}
            />
          </div>
          <div className="flex-1">
            <Input
              label="Exam Rooms"
              tooltip="How many exam rooms are in your hospital?"
              type="number"
              {...register('examRoomsCount')}
              error={errors.examRoomsCount?.message}
            />
          </div>
        </Flex>
      </div>

      {isPurchasingPreferencesEnabled && (
        <div className="mt-3 border border-black/[0.04] bg-white p-6">
          <Text c="#344054" size="16px" fw="500">
            Shopping Preferences
          </Text>
          <Divider my="16px" />
          <div className="mb-4">
            <Select
              label="Main Priority"
              options={shoppingPreferencesOptions}
              {...register('primaryShoppingPreference')}
              error={errors.primaryShoppingPreference?.message}
            />
          </div>
          <div className="mb-4">
            <MultiSelect
              label="Secondary"
              placeholder="Select options"
              {...register('secondaryShoppingPreferences')}
              defaultValue={defaultValues.secondaryShoppingPreferences || []}
              options={shoppingPreferencesOptions}
            />
          </div>
        </div>
      )}
      <Flex mb="24px" mt="12px">
        <Button disabled={!isValid} loading={isLoading} variant="secondary">
          Save
        </Button>
      </Flex>
    </form>
  );
};
