import { FEATURE_FLAGS } from '@/constants';
import * as Yup from 'yup';

export const speciesFocusOptions = [
  { value: 'canine', label: 'Canine' },
  { value: 'feline', label: 'Feline' },
  { value: 'equine', label: 'Equine' },
  { value: 'production_or_large_animal', label: 'Production or Large Animal' },
  { value: 'exotics', label: 'Exotics' },
  { value: 'avian', label: 'Avian' },
  { value: 'marine', label: 'Marine' },
];

export const practiceTypeOptions = [
  { value: 'general', label: 'General' },
  { value: 'mobile', label: 'Mobile' },
  { value: 'emergency_and_urgent_care', label: 'Emergency and Urgent Care' },
  { value: 'specialty_referral', label: 'Specialty Referral' },
  { value: 'large_animal', label: 'Large Animal' },
  { value: 'mixed_animal', label: 'Mixed Animal' },
  { value: 'exotic_avian_and_zoo', label: 'Exotic, Avian and Zoo' },
  { value: 'academic', label: 'Academic' },
  { value: 'laboratory', label: 'Laboratory' },
  { value: 'holistic_and_integrative', label: 'Holistic and Integrative' },
];

export const createSchema = (isPurchasingPreferencesEnabled: boolean) => Yup.object({
  speciesFocus: Yup.string()
    .required('Species Focus is a required field')
    .max(255),
  practiceTypes: Yup.array(),
  examRoomsCount: Yup.number()
    .transform((value, originalValue) => {
      return originalValue === '' ? undefined : value;
    })
    .min(1, 'Exam Rooms must be greater than or equal to 1'),
  fulltimeDvmCount: Yup.number()
    .transform((value, originalValue) => {
      return originalValue === '' ? undefined : value;
    })
    .min(1, 'Fulltime DVM must be greater than or equal to 1'),
  ...(isPurchasingPreferencesEnabled
    ? {
        primaryShoppingPreference: Yup.string()
          .required('Primary Shopping Preference is a required field')
          .max(255),
        secondaryShoppingPreferences: Yup.array(),
      }
    : {}),
});

// Backward compatibility - keep the original SCHEMA for existing code
export const SCHEMA = createSchema(FEATURE_FLAGS.PURCHASING_PREFERENCES);
