import { useEffect, useMemo } from 'react';
import { Button, Group, Text } from '@mantine/core';
import { useSearchParams } from 'react-router-dom';
import { Trans, useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { useQuery } from '@tanstack/react-query';

import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { scrollToElement } from '@/utils';
import { getEndIndex, getStartIndex } from '@/libs/ui/Pagination/utils';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { queryKeys } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';

import type { ProductType } from '@/types';
import styles from './search.module.css';
import { ProductSearchCardList } from './components/ProductSearchCardList/ProductSearchCardList';
import ArrowUpArrowDownIcon from '@/assets/images/product/arrow-down-arrow-up.svg?react';
import { FEATURE_FLAGS } from '@/constants';
import { SearchFilter } from './components/SearchFilter/SearchFilter';

export const Search = () => {
  const { t } = useTranslation();
  const [query, setQueryParams] = useSearchParams(
    new URLSearchParams(window.location.search),
  );
  const { clinic } = useClinicStore();

  // Extract search parameters from URL
  const searchParams = useMemo(() => {
    const params = Object.fromEntries(query);
    return {
      query: params.query?.trim() || '',
      page: parseInt(params.page || '1'),
      perPage: params.perPage || '12',
      vendorIds: params.vendorIds || '',
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    };
  }, [query]);

  // Build query string for API call
  const queryString = useMemo(() => {
    if (!searchParams.query) return '';

    const { page, perPage, vendorIds, ...rest } = searchParams;
    let newQuery = new URLSearchParams(rest).toString();

    newQuery += page ? `&page[number]=${page}` : '';
    newQuery += perPage ? `&page[size]=${perPage}` : '';
    newQuery += vendorIds ? `&filter[vendorIds]=${vendorIds}` : '';

    return newQuery;
  }, [searchParams]);

  // React Query for search
  const {
    data: searchResults,
    isLoading: isSearchLoading,
    error: hasError,
  } = useQuery({
    queryKey: queryKeys.products.search(clinic?.id || '', queryString),
    queryFn: async () => {
      if (!searchParams.query || !clinic?.id) {
        return { data: [], meta: { total: 0 } };
      }

      const response = await get<{
        data: ProductType[];
        meta: { total: number };
      }>({
        url: `/clinics/${clinic.id}/search?${queryString}`,
      });

      return response;
    },
    enabled: !!searchParams.query && !!clinic?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const productList = searchResults?.data || [];
  const total = searchResults?.meta.total || 0;
  const { page, perPage } = searchParams;

  const searchValue = searchParams.query;
  const startIndex = getStartIndex(page, perPage);
  const endIndex = getEndIndex(startIndex, perPage, total);

  const handleChangeItemsPerPage = (newPerPage: string) => {
    const newParams = new URLSearchParams(query);
    newParams.set('perPage', newPerPage);
    newParams.set('page', '1');
    setQueryParams(newParams);
    scrollToElement('searchScrollToTop');
  };

  const handleChangePage = (value: number) => {
    const newParams = new URLSearchParams(query);
    newParams.set('page', value.toString());
    setQueryParams(newParams);
    scrollToElement('searchScrollToTop');
  };

  const setHubSpotPageTracking = (reset: boolean = false) => {
    // @ts-expect-error - HubSpot global variable
    const _hsq = window._hsq || [];

    if (reset) {
      _hsq.push(['setPath', '/']);

      return;
    }

    _hsq.push(['setPath', SHOP_ROUTES_PATH.search]);
  };

  useEffect(() => {
    setHubSpotPageTracking();

    return () => {
      setHubSpotPageTracking(true);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      id="searchScrollToTop"
      className={clsx(styles.container, 'mainSection')}
    >
      <PageHeader title={t('client.search.title')} />

      <div className={clsx(styles.content, 'pageContent')}>
        <div className={styles.contentTitle}>
          <div>
            <h3>Search Results</h3>
            <Text span size="lgMd">
              <Trans
                i18nKey="client.search.showingResults"
                values={{
                  totalPageStart: startIndex,
                  totalPageEnd: endIndex,
                  total,
                  searchValue,
                }}
                components={{ searchValue: <Text span fs="italic" /> }}
              />
            </Text>
          </div>

          <Group justify="center">
            {FEATURE_FLAGS.SEARCH_RESULTS_FILTER && (
              <SearchFilter
                onChange={(filters) => {
                  const newParams = new URLSearchParams(query);
                  Object.entries(filters).forEach(([key, value]) => {
                    if (value) {
                      newParams.set(key, value.toString());
                    } else {
                      newParams.delete(key);
                    }
                  });
                  newParams.set('page', '1'); // Reset to first page
                  setQueryParams(newParams);
                }}
                initialValue={{ vendorIds: query.get('vendorIds') || '' }}
              />
            )}
            {FEATURE_FLAGS.SEARCH_RESULTS_ORDER && (
              <Button
                mr="10"
                leftSection={<ArrowUpArrowDownIcon />}
                variant="default"
              >
                Order by
              </Button>
            )}
          </Group>
        </div>

        <ProductSearchCardList
          searchValue={searchValue}
          productList={productList}
          isSearchLoading={isSearchLoading}
          hasError={!!hasError}
        />
      </div>

      <Pagination
        itemsPerPage={perPage.toString()}
        page={page}
        total={total}
        onPageChange={handleChangePage}
        onChangeItemsPerPage={handleChangeItemsPerPage}
        limitOptions={[
          {
            value: '12',
            label: '12',
          },
          {
            value: '24',
            label: '24',
          },
          {
            value: '36',
            label: '36',
          },
          {
            value: '48',
            label: '48',
          },
        ]}
      />
    </div>
  );
};
