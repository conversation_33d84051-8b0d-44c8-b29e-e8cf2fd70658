import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { LoginForm } from '@/libs/auth';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';
import { useRememberMe } from './hooks/useRememberMe';

const BASE_URL = import.meta.env.VITE_API_URL || 'https://127.0.0.1:8000';

interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const Login = () => {
  const navigate = useNavigate();
  const { reset, setError } = useForm<FormValues>({
    resolver: yup<PERSON><PERSON><PERSON>ver(SCHEMA),
  });

  const { setRememberMeValues } = useRememberMe({
    onLoadValues: reset,
  });

  const { apiRequest: handleLogin, isLoading } = useAsyncRequest({
    apiFunc: async (values?: FormValues) => {
      if (!values) return;

      const { email, password } = values;

      const userData = await post<
        UserType & { clinicId: string | null; clinic_id: string | null }
      >({
        url: '/sessions',
        body: { email, password },
        withApi: false,
      });

      setRememberMeValues(values);

      const queryParameters = new URLSearchParams(window.location.search);

      // TODO: Extract Amazon code from here
      const amazonCallbackUri = queryParameters.get('amazon_callback_uri');
      // TODO: This line should be changed in the future when the BE change the field name from clinic_id to clinicId
      const clinicId = userData.clinicId ?? userData.clinic_id;

      if (amazonCallbackUri && clinicId) {
        const amazonState = queryParameters.get('amazon_state');
        const amazonParams =
          '?amazon_state=' +
          amazonState +
          '&state=' +
          clinicId +
          '&redirect_uri=' +
          `${BASE_URL}/api/oauth2/amazon-business` +
          '&status=authentication_successful';

        window.location.href = amazonCallbackUri + amazonParams;

        return;
      }

      navigate(SHOP_ROUTES_PATH.home);
    },
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const handleSubmit = async (values: FormValues) => {
    await handleLogin(values);
  };

  return (
    <LoginForm
      onSubmit={handleSubmit}
      isLoading={isLoading}
      forgotPasswordPath={SHOP_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
    />
  );
};
