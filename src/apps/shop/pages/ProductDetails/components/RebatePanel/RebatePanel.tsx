import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { get } from '@/libs/utils/api';
import { Box, Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useEffect, useState, startTransition } from 'react';

interface Rebate {
  description: string;
  id: string;
  name: string;
  type: string;
}

interface RebatePanelProps {
  productOfferId?: string;
}
export const RebatePanel = ({ productOfferId }: RebatePanelProps) => {
  const [rebates, setRebates] = useState<Rebate[]>([]);

  // TODO: Handle loading and error
  const { apiRequest } = useAsyncRequest({
    apiFunc: async () => {
      const response = await get<Rebate[]>({
        url: `/promotions?filter[type]=rebate&filter[product_offer_id]=${productOfferId}`,
      });

      return setRebates(response);
    },
  });

  useEffect(() => {
    startTransition(() => {
      apiRequest();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return rebates.length > 0 ? (
    <Box mb="2rem">
      <CollapsiblePanel
        header={
          <Flex h="100%" align="center" ml="1.5rem">
            <Text size="md" fw={500}>
              Rebates
            </Text>
          </Flex>
        }
        content={
          <Box p="md" mb="lg">
            {rebates.map(({ id, name, description }, index) => (
              <Box key={id}>
                {index !== 0 ? <Divider my="md" /> : null}
                <Text fw="500" mb="sm">
                  {name}
                </Text>
                <MarkdownRenderer markdown={description} />
              </Box>
            ))}
          </Box>
        }
        startOpen
      />
    </Box>
  ) : null;
};
