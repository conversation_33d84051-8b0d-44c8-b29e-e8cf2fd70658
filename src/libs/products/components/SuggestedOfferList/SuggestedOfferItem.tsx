import { Link } from 'react-router-dom';
import { OfferType } from '@/types';
import { Box, Divider, Group, Image, Modal, Stack, Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { getPriceString } from '@/utils';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useState, type FormEventHandler } from 'react';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '../AddToCartInput/AddToCartInput';
import { AddToCartButton } from '../AddToCartForm/components/AddToCartButton/AddToCartButton';
import styles from './SuggestedOfferItem.module.css';
import { stockStatusConfigs } from '../StockStatusIcon/StockStatusIcon';
import { useDisclosure } from '@mantine/hooks';
import { PurchaseHistoryChart } from '../PurchaseHistory/PurchaseHistoryChart';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Flex } from '@/libs/ui/Flex/Flex';

type OfferSubstitutesItemProps = {
  offer: OfferType;
};

export const SuggestedOfferItem = ({ offer }: OfferSubstitutesItemProps) => {
  const {
    id,
    increments,
    name,
    price,
    product,
    vendor,
    vendorSku,
    stockStatus,
  } = offer;
  const [isModalOpen, { open, close }] = useDisclosure(false);
  const { addToCart, updatingProductIds } = useCartStore();
  const [amount, setAmount] = useState(increments ?? 1);
  const cartProductMapState = useCartProductMapState();

  const isUpdating = updatingProductIds.has(id);

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const handleAddToCartClick: FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    addToCart({
      productOfferId: id,
      quantity: offerQuantityOnCart + amount,
      // TODO: Handle error better
      onError: () => {},
    });
  };
  const offerQuantityOnCart = cartProductMapState[id]?.quantity ?? 0;
  const productUrl = getProductUrl(product.id, id);

  return (
    <Flex justify="space-between" className={styles.container}>
      <Stack gap="0">
        <Link to={productUrl} className={styles.link}>
          <Text className={styles.title} lh="1.7">
            {name}
          </Text>
        </Link>
        <Group align="center" gap="0.5rem">
          <Image src={vendor?.imageUrl} alt={vendor?.name} h={22} />
          <Text size="xs" c="#666666CC">
            SKU:
          </Text>
          <Text size="xs" c="#333">
            {vendorSku}
          </Text>
          <Divider orientation="vertical" />
          <Text size="xs" fw={500} c="green.0">
            {stockStatusConfigs[stockStatus]?.name}
          </Text>
          <Divider orientation="vertical" />
          <Button
            variant="unstyled"
            onClick={open}
            style={{ padding: '0.375rem 0' }}
          >
            <Text size="0.75rem" c="#447BFD" fw="500">
              Purchase History
            </Text>
          </Button>
        </Group>
      </Stack>
      <Group>
        <Text size="sm" fw={500} c="#333333">
          {getPriceString(price)}
        </Text>
        <form onSubmit={handleAddToCartClick}>
          <Group wrap="nowrap" gap="0.5rem">
            <Box w="60px">
              <AddToCartInput
                hideButtons
                originalAmount={increments}
                minIncrement={increments}
                onUpdate={handleQuantityUpdate}
                size="sm"
              />
            </Box>
            <Box w="auto">
              <AddToCartButton
                quantityInCart={offerQuantityOnCart}
                isLoading={isUpdating}
                fullWidth
                size="sm"
                onlyIcon
              />
            </Box>
          </Group>
        </form>
      </Group>
      <Modal
        opened={isModalOpen}
        onClose={close}
        title="Purchase History"
        size="auto"
      >
        <Box p="md">
          <PurchaseHistoryChart productId={offer.id} />
        </Box>
      </Modal>
    </Flex>
  );
};
