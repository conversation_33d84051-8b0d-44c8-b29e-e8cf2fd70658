import { Box } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import styles from './Tabs.module.css';
interface TabsProps {
  active: number;
  tabs: {
    label: string;
    onClick: (index: number) => void;
  }[];
}
export const Tabs = ({ tabs, active }: TabsProps) => {
  return (
    <Flex bg="#f1f2f4" style={{ borderRadius: '0.5rem' }}>
      {tabs.map(({ label, onClick }, index) => (
        <Box flex="1" key={label}>
          <Button
            variant="unstyled"
            onClick={() => onClick(index)}
            className={active === index ? styles.activeTab : ''}
            style={{
              width: '100%',
              height: '40px',
              textAlign: 'center',
              borderRadius: '0.5rem',
            }}
          >
            {label}
          </Button>
        </Box>
      ))}
    </Flex>
  );
};
