import { ReactNode } from 'react';
import { Text } from '@mantine/core';

import styles from './AuthForm.module.css';

interface AuthFormProps {
  title: string;
  subtitle?: string | ReactNode;
  children: ReactNode;
  footer?: ReactNode;
}

export const AuthForm = ({
  title,
  subtitle,
  children,
  footer,
}: AuthFormProps) => {
  return (
    <>
      <Text ta="center" size="xlLg" className={styles.title}>
        {title}
      </Text>

      {subtitle && (
        <Text ta="center" size="md" className={styles.subtitle}>
          {subtitle}
        </Text>
      )}

      <div className={styles.form}>{children}</div>

      {footer && <div className={styles.footer}>{footer}</div>}
    </>
  );
};
