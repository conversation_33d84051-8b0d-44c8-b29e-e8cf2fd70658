import { KeyboardEvent } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { AuthForm } from '../AuthForm';
import styles from './LoginForm.module.css';

interface LoginFormProps {
  onSubmit: (values: FormValues) => Promise<void>;
  isLoading?: boolean;
  forgotPasswordPath: string;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const LoginForm = ({
  onSubmit,
  isLoading = false,
  forgotPasswordPath,
  schema,
  namespace = 'login',
}: LoginFormProps) => {
  const { t } = useTranslation();
  const { register, handleSubmit } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const handleKeyPress = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleLogin();
    }
  };

  const handleLogin = handleSubmit(async (values) => {
    await onSubmit(values);
  });

  return (
    <AuthForm
      title={t(`${namespace}.title`)}
      subtitle={t(`${namespace}.subtitle`)}
    >
      <Input
        label={t('form.field.email')}
        placeholder={t('form.field.email')}
        {...register('email')}
        onKeyDown={handleKeyPress}
        disabled={isLoading}
        size="md"
      />

      <Input
        type="password"
        label={t('form.field.password')}
        placeholder={t('form.field.password')}
        {...register('password')}
        onKeyDown={handleKeyPress}
        disabled={isLoading}
        size="md"
      />

      <div className={styles.info}>
        <Checkbox
          label={t(`${namespace}.rememberMe`)}
          {...register('rememberMe')}
        />

        <Link to={forgotPasswordPath}>{t(`${namespace}.forgotPassword`)}</Link>
      </div>

      <Button
        onClick={handleLogin}
        loading={isLoading}
        className={styles.submitButton}
      >
        {t(`${namespace}.signIn`)}
      </Button>
    </AuthForm>
  );
};
