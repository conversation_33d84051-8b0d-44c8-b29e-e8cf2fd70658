import { Suspense, useEffect, ReactNode } from 'react';
import { Outlet } from 'react-router-dom';
import { LoadingOverlay, Box } from '@mantine/core';

import { Logo } from '@/libs/ui/Logo/Logo';

import styles from './AuthLayout.module.css';

const LOADER_PROPS = {
  mt: '250px',
  size: '3rem',
};

export interface AuthLayoutProps {
  bg?: string;
  innerBg?: string;
  children?: ReactNode;
}

export const AuthLayout = ({
  bg = 'transparent',
  innerBg = 'transparent',
  children,
}: AuthLayoutProps) => {
  useEffect(() => {
    // resetAllStores();
  }, []);

  return (
    <div className={styles.wrapper} style={{ background: bg }}>
      <div className={styles.innerContent} style={{ background: innerBg }}>
        <div className={styles.logoWrapper}>
          <Logo className={styles.logo} />
        </div>
        <Box component="main">
          {children ? (
            children
          ) : (
            <Suspense
              fallback={<LoadingOverlay loaderProps={LOADER_PROPS} visible />}
            >
              <Outlet />
            </Suspense>
          )}
        </Box>
      </div>
    </div>
  );
};
