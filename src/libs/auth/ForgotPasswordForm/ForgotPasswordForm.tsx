import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { AuthForm } from '../AuthForm';
import styles from './ForgotPasswordForm.module.css';

interface ForgotPasswordFormProps {
  onSubmit: (values: FormValues) => Promise<void>;
  isLoading?: boolean;
  loginPath: string;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

interface FormValues {
  email: string;
}

export const ForgotPasswordForm = ({
  onSubmit,
  isLoading = false,
  loginPath,
  schema,
  namespace = 'forgotPassword',
}: ForgotPasswordFormProps) => {
  const { t } = useTranslation();
  const { register, handleSubmit } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const handleResetPassword = handleSubmit(async (values) => {
    await onSubmit(values);
  });

  return (
    <AuthForm
      title={t(`${namespace}.title`)}
      subtitle={t(`${namespace}.subtitle`)}
      footer={
        <>
          {t(`${namespace}.haveAccount`)}
          <Link to={loginPath}>{t(`${namespace}.logIn`)}</Link>
        </>
      }
    >
      <Input
        label={t('form.field.email')}
        placeholder={t('form.field.email')}
        {...register('email')}
        size="md"
      />

      <Button
        onClick={handleResetPassword}
        loading={isLoading}
        className={styles.submitButton}
      >
        {t(`${namespace}.submit`)}
      </Button>
    </AuthForm>
  );
};
