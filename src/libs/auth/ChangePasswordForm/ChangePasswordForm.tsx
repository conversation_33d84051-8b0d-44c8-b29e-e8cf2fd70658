import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation, Trans } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { AuthForm } from '../AuthForm';
import styles from './ChangePasswordForm.module.css';

interface ChangePasswordFormProps {
  onSubmit: (values: FormValues) => Promise<void>;
  isLoading?: boolean;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

interface FormValues {
  password: string;
  confirmPassword: string;
}

export const ChangePasswordForm = ({
  onSubmit,
  isLoading = false,
  schema,
  namespace = 'changePassword',
}: ChangePasswordFormProps) => {
  const { t } = useTranslation();
  const { register, handleSubmit } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const handleChangePassword = handleSubmit(async (values) => {
    await onSubmit(values);
  });

  return (
    <AuthForm
      title={t(`${namespace}.title`)}
      subtitle={
        <Trans
          i18nKey={`${namespace}.subtitle`}
          components={{ b: <b />, br: <br /> }}
          values={{ email: '' }}
        />
      }
    >
      <Input
        type="password"
        label={t('form.field.password')}
        placeholder={t('form.field.password')}
        {...register('password')}
        size="md"
      />

      <Input
        type="password"
        label={t('form.field.confirmPassword')}
        placeholder={t('form.field.confirmPassword')}
        {...register('confirmPassword')}
        size="md"
      />

      <Button
        onClick={handleChangePassword}
        loading={isLoading}
        className={styles.submitButton}
      >
        {t(`${namespace}.confirm`)}
      </Button>
    </AuthForm>
  );
};
