import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { FEATURE_FLAGS } from '@/constants';

type FeatureFlagKey = keyof typeof FEATURE_FLAGS;
type FeatureFlags = typeof FEATURE_FLAGS;

/**
 * Hook that returns feature flags with URL search parameter overrides
 * 
 * Usage:
 * - Default: const flags = useFeatureFlags();
 * - URL override: ?PRODUCT_REVIEWS=true&SUBSTITUTES=false
 * 
 * @returns Feature flags object with URL parameter overrides applied
 */
export const useFeatureFlags = (): FeatureFlags => {
  const [searchParams] = useSearchParams();

  const featureFlags = useMemo(() => {
    // Start with default feature flags
    const flags = { ...FEATURE_FLAGS };

    // Override with URL search parameters
    Object.keys(FEATURE_FLAGS).forEach((key) => {
      const flagKey = key as FeatureFlagKey;
      const urlValue = searchParams.get(flagKey);
      
      if (urlValue !== null) {
        // Convert string to boolean
        // 'true', '1', 'yes', 'on' = true
        // 'false', '0', 'no', 'off' = false
        // anything else = false
        const booleanValue = ['true', '1', 'yes', 'on'].includes(
          urlValue.toLowerCase()
        );
        
        flags[flagKey] = booleanValue;
      }
    });

    return flags;
  }, [searchParams]);

  return featureFlags;
};

/**
 * Hook that returns a specific feature flag with URL search parameter override
 * 
 * Usage:
 * const isProductReviewsEnabled = useFeatureFlag('PRODUCT_REVIEWS');
 * 
 * @param flagKey - The feature flag key to check
 * @returns Boolean value of the feature flag
 */
export const useFeatureFlag = (flagKey: FeatureFlagKey): boolean => {
  const flags = useFeatureFlags();
  return flags[flagKey];
};

/**
 * Utility function to get feature flags with URL overrides (for use outside React components)
 * 
 * @param searchParams - URLSearchParams object
 * @returns Feature flags object with URL parameter overrides applied
 */
export const getFeatureFlagsWithUrlOverrides = (
  searchParams: URLSearchParams
): FeatureFlags => {
  const flags = { ...FEATURE_FLAGS };

  Object.keys(FEATURE_FLAGS).forEach((key) => {
    const flagKey = key as FeatureFlagKey;
    const urlValue = searchParams.get(flagKey);
    
    if (urlValue !== null) {
      const booleanValue = ['true', '1', 'yes', 'on'].includes(
        urlValue.toLowerCase()
      );
      
      flags[flagKey] = booleanValue;
    }
  });

  return flags;
};
