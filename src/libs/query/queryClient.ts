import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    },
  },
});

export const queryKeys = {
  products: {
    all: ['products'] as const,
    suggestions: (clinicId: string, query: string) => [
      ...queryKeys.products.all,
      'suggestions',
      clinicId,
      query,
    ],
  },
  rebates: {
    all: ['rebates'] as const,
    estimates: () => [...queryKeys.rebates.all, 'estimates'],
  },
} as const;
